import Adventurer from '../entities/Adventurer.js';
import AdventurerTeam from '../entities/AdventurerTeam.js';

/**
 * 冒險者管理系統
 */
export default class AdventurerSystem {
  /**
   * 創建冒險者管理系統
   * @param {Object} resourceSystem - 資源系統引用
   * @param {Object} buildingSystem - 建築系統引用
   */
  constructor(resourceSystem, buildingSystem) {
    this.resourceSystem = resourceSystem;
    this.buildingSystem = buildingSystem;
    this.isActive = false;
    this.teams = new Map();
    this.availableQuests = new Map();
    this.completedQuests = [];
    this.questIdCounter = 1;
    
    // 初始化任務類型
    this.initializeQuestTypes();
    
    // 檢查冒險者公會是否存在
    this.checkAdventurerGuild();
  }

  /**
   * 檢查冒險者公會建築
   */
  checkAdventurerGuild() {
    if (this.buildingSystem) {
      const guild = this.buildingSystem.getBuildingsByType('adventurer_guild');
      const wasActive = this.isActive;
      this.isActive = guild.length > 0 && guild.some(building => building.isActive);

      // 如果剛剛啟動，初始化系統
      if (!wasActive && this.isActive) {
        this.activate();
      }
    }
  }

  /**
   * 初始化任務類型
   */
  initializeQuestTypes() {
    this.questTypes = {
      resource_gathering: {
        name: '資源收集',
        description: '前往指定地點收集特殊資源',
        baseReward: { gold: 100 },
        baseDifficulty: 80,
        estimatedDays: 2
      },
      monster_clearing: {
        name: '魔獸清理',
        description: '清理郊區的危險魔獸',
        baseReward: { gold: 150 },
        baseDifficulty: 120,
        estimatedDays: 3
      },
      escort_mission: {
        name: '護送任務',
        description: '護送商隊或重要人物',
        baseReward: { gold: 80 },
        baseDifficulty: 60,
        estimatedDays: 1
      },
      exploration: {
        name: '探索任務',
        description: '探索未知區域並繪製地圖',
        baseReward: { gold: 200 },
        baseDifficulty: 100,
        estimatedDays: 4
      },
      special_delivery: {
        name: '特殊運送',
        description: '運送重要物品到指定地點',
        baseReward: { gold: 120 },
        baseDifficulty: 70,
        estimatedDays: 2
      }
    };
  }

  /**
   * 啟動系統
   */
  activate() {
    this.isActive = true;
    this.generateInitialTeams();
    this.generateRandomQuests();
  }

  /**
   * 生成初始團隊
   */
  generateInitialTeams() {
    // 生成2-4個初始團隊
    const teamCount = 2 + Math.floor(Math.random() * 3);
    
    for (let i = 0; i < teamCount; i++) {
      const team = this.generateRandomTeam();
      this.teams.set(team.id, team);
    }
  }

  /**
   * 生成隨機團隊
   */
  generateRandomTeam() {
    const team = new AdventurerTeam();
    
    // 隨機生成2-4個成員
    const memberCount = 2 + Math.floor(Math.random() * 3);
    
    for (let i = 0; i < memberCount; i++) {
      const adventurer = this.generateRandomAdventurer();
      team.addMember(adventurer);
    }
    
    // 設置初始資金和物資
    team.funds = 50 + Math.floor(Math.random() * 100);
    team.addSupplies({
      '乾糧': 10 + Math.floor(Math.random() * 10),
      '藥水': 5 + Math.floor(Math.random() * 5),
      '繩索': 2 + Math.floor(Math.random() * 3)
    }, 3 + Math.floor(Math.random() * 4));
    
    // 30%機率是本地常駐團隊
    team.isLocalResident = Math.random() < 0.3;
    
    return team;
  }

  /**
   * 生成隨機冒險者
   */
  generateRandomAdventurer() {
    const professions = ['warrior', 'mage', 'archer', 'rogue', 'cleric'];
    const profession = professions[Math.floor(Math.random() * professions.length)];
    
    return new Adventurer({
      profession: profession,
      level: 1 + Math.floor(Math.random() * 3)
    });
  }

  /**
   * 生成隨機任務
   */
  generateRandomQuests() {
    // 生成3-6個隨機任務
    const questCount = 3 + Math.floor(Math.random() * 4);
    
    for (let i = 0; i < questCount; i++) {
      const quest = this.generateRandomQuest();
      this.availableQuests.set(quest.id, quest);
    }
  }

  /**
   * 生成隨機任務
   */
  generateRandomQuest() {
    const typeKeys = Object.keys(this.questTypes);
    const typeKey = typeKeys[Math.floor(Math.random() * typeKeys.length)];
    const questType = this.questTypes[typeKey];
    
    const quest = {
      id: this.questIdCounter++,
      type: typeKey,
      name: questType.name,
      description: questType.description,
      difficulty: questType.baseDifficulty + Math.floor(Math.random() * 40) - 20,
      estimatedDays: questType.estimatedDays + Math.floor(Math.random() * 2),
      rewards: this.generateQuestRewards(questType),
      requirements: this.generateQuestRequirements(),
      createdTime: Date.now(),
      expiryTime: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7天後過期
      isCompleted: false
    };
    
    return quest;
  }

  /**
   * 生成任務獎勵
   */
  generateQuestRewards(questType) {
    const rewards = { ...questType.baseReward };
    
    // 隨機添加資源獎勵
    if (Math.random() < 0.6) {
      const resources = ['magic_ore', 'enchanted_wood', 'arcane_crystal', 'mana'];
      const resource = resources[Math.floor(Math.random() * resources.length)];
      rewards[resource] = 5 + Math.floor(Math.random() * 15);
    }
    
    // 隨機調整金幣獎勵
    rewards.gold = Math.floor(rewards.gold * (0.8 + Math.random() * 0.4));
    
    return rewards;
  }

  /**
   * 生成任務需求
   */
  generateQuestRequirements() {
    return {
      minRank: ['F', 'E', 'D', 'C'][Math.floor(Math.random() * 4)],
      minMembers: 2 + Math.floor(Math.random() * 2),
      minCombatPower: 100 + Math.floor(Math.random() * 200)
    };
  }

  /**
   * 發布任務
   */
  publishQuest(questData) {
    const quest = {
      id: this.questIdCounter++,
      ...questData,
      createdTime: Date.now(),
      expiryTime: Date.now() + (questData.duration || 7) * 24 * 60 * 60 * 1000,
      isCompleted: false
    };
    
    this.availableQuests.set(quest.id, quest);
    
    return { success: true, quest: quest, message: `任務 "${quest.name}" 已發布` };
  }

  /**
   * 分配任務給團隊
   */
  assignQuestToTeam(questId, teamId) {
    const quest = this.availableQuests.get(questId);
    const team = this.teams.get(teamId);
    
    if (!quest) {
      return { success: false, message: '任務不存在' };
    }
    
    if (!team) {
      return { success: false, message: '團隊不存在' };
    }
    
    // 檢查團隊是否符合任務要求
    const meetsRequirements = this.checkTeamRequirements(team, quest);
    if (!meetsRequirements.success) {
      return meetsRequirements;
    }
    
    // 團隊接受任務
    const result = team.acceptQuest(quest);
    if (result.success) {
      this.availableQuests.delete(questId);
    }
    
    return result;
  }

  /**
   * 檢查團隊是否符合任務要求
   */
  checkTeamRequirements(team, quest) {
    const requirements = quest.requirements || {};
    
    // 檢查等級要求
    if (requirements.minRank) {
      const rankOrder = ['F', 'E', 'D', 'C', 'B', 'A', 'S', 'SS', 'SSS'];
      const teamRankIndex = rankOrder.indexOf(team.rank);
      const requiredRankIndex = rankOrder.indexOf(requirements.minRank);
      
      if (teamRankIndex < requiredRankIndex) {
        return { success: false, message: `團隊等級不足，需要 ${requirements.minRank} 級以上` };
      }
    }
    
    // 檢查成員數量
    if (requirements.minMembers && team.members.length < requirements.minMembers) {
      return { success: false, message: `團隊成員不足，需要至少 ${requirements.minMembers} 人` };
    }
    
    // 檢查戰鬥力
    if (requirements.minCombatPower && team.getTotalCombatPower() < requirements.minCombatPower) {
      return { success: false, message: `團隊戰鬥力不足，需要至少 ${requirements.minCombatPower}` };
    }
    
    return { success: true };
  }

  /**
   * 更新系統狀態
   */
  update(deltaTime) {
    if (!this.isActive) {
      this.checkAdventurerGuild();
      return;
    }
    
    // 檢查團隊任務完成情況
    this.teams.forEach(team => {
      if (team.checkQuestCompletion()) {
        this.completeTeamQuest(team);
      }
    });
    
    // 清理過期任務
    this.cleanupExpiredQuests();
    
    // 隨機生成新任務
    if (Math.random() < 0.001) { // 0.1%機率每次更新
      const quest = this.generateRandomQuest();
      this.availableQuests.set(quest.id, quest);
    }
  }

  /**
   * 完成團隊任務
   */
  completeTeamQuest(team) {
    const quest = team.currentQuest;
    if (!quest) return;
    
    // 計算成功率
    const successRate = team.calculateQuestSuccessRate(quest);
    const isSuccess = Math.random() < successRate;
    
    if (isSuccess) {
      // 任務成功
      const result = team.completeQuest();
      if (result.success) {
        // 給予獎勵
        this.giveQuestRewards(quest.rewards);
        this.completedQuests.push({
          ...quest,
          completedTime: Date.now(),
          teamId: team.id,
          teamName: team.name,
          success: true
        });
      }
    } else {
      // 任務失敗
      team.currentQuest = null;
      team.questStartTime = null;
      team.consumeSupplies(quest.estimatedDays);
      
      this.completedQuests.push({
        ...quest,
        completedTime: Date.now(),
        teamId: team.id,
        teamName: team.name,
        success: false
      });
    }
  }

  /**
   * 給予任務獎勵
   */
  giveQuestRewards(rewards) {
    Object.entries(rewards).forEach(([rewardType, amount]) => {
      if (rewardType === 'gold') {
        // 增加金幣
        if (this.resourceSystem.gameState) {
          this.resourceSystem.gameState.playerGold += amount;
        }
      } else {
        // 增加資源
        if (this.resourceSystem.resources[rewardType]) {
          this.resourceSystem.addResources({ [rewardType]: amount });
        }
      }
    });
  }

  /**
   * 清理過期任務
   */
  cleanupExpiredQuests() {
    const now = Date.now();
    this.availableQuests.forEach((quest, questId) => {
      if (quest.expiryTime < now) {
        this.availableQuests.delete(questId);
      }
    });
  }

  /**
   * 獲取系統狀態
   */
  getStatus() {
    return {
      isActive: this.isActive,
      teamCount: this.teams.size,
      teams: Array.from(this.teams.values()).map(team => team.getStatus()),
      availableQuestCount: this.availableQuests.size,
      availableQuests: Array.from(this.availableQuests.values()),
      completedQuestCount: this.completedQuests.length,
      recentCompletedQuests: this.completedQuests.slice(-10)
    };
  }

  /**
   * 獲取團隊列表
   */
  getTeams() {
    return Array.from(this.teams.values());
  }

  /**
   * 獲取可用任務列表
   */
  getAvailableQuests() {
    return Array.from(this.availableQuests.values());
  }

  /**
   * 獲取特定團隊
   */
  getTeam(teamId) {
    return this.teams.get(teamId);
  }

  /**
   * 獲取特定任務
   */
  getQuest(questId) {
    return this.availableQuests.get(questId);
  }

  /**
   * 招募新團隊
   */
  recruitTeam() {
    if (!this.isActive) {
      return { success: false, message: '冒險者公會未啟動' };
    }

    const team = this.generateRandomTeam();
    this.teams.set(team.id, team);

    return { success: true, team: team, message: `新團隊 "${team.name}" 加入了公會` };
  }

  /**
   * 解散團隊
   */
  disbandTeam(teamId) {
    const team = this.teams.get(teamId);
    if (!team) {
      return { success: false, message: '團隊不存在' };
    }

    if (team.currentQuest) {
      return { success: false, message: '團隊正在執行任務，無法解散' };
    }

    this.teams.delete(teamId);
    return { success: true, message: `團隊 "${team.name}" 已解散` };
  }

  /**
   * 為團隊補充物資
   */
  supplyTeam(teamId, supplies, days, cost) {
    const team = this.teams.get(teamId);
    if (!team) {
      return { success: false, message: '團隊不存在' };
    }

    // 檢查金幣是否足夠
    if (this.resourceSystem.gameState && this.resourceSystem.gameState.playerGold < cost) {
      return { success: false, message: '金幣不足' };
    }

    // 扣除金幣
    if (this.resourceSystem.gameState) {
      this.resourceSystem.gameState.playerGold -= cost;
    }

    // 添加物資
    team.addSupplies(supplies, days);

    return { success: true, message: `已為團隊 "${team.name}" 補充物資` };
  }
}
